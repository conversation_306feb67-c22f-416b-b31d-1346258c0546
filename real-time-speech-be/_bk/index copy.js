const WebSocket = require('ws');
const express = require('express');
const http = require('http');
const cors = require('cors');
const axios = require('axios');
const FormData = require('form-data');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuración desde variables de entorno
const config = {
  port: process.env.PORT || 8080,
  ia: {
    apiUrl: process.env.VITE_IA_API_URL || "https://dev.dl2discovery.org/llm-api/v1/",
    apiKey: process.env.VITE_IA_API_KEY || "9dcd0147-11e2-4e9e-aaf3-05e1498ce828",
    presets: {
      genCharBot: process.env.VITE_IA_PRESETID_GENCHARBOT || "mapp-gen-char-bot",
      iaVsPlayer: process.env.VITE_IA_PRESETID_IA_VS_PLAYER || "mapp-Claude_enygma_V2"
    }
  },
  audio: {
    s2tUrl: `${process.env.AUDIO_BACKEND_BASE_URL}/s2t` || "https://dev.dl2discovery.org/sts/api/v1/s2t",
    t2sUrl: `${process.env.AUDIO_BACKEND_BASE_URL}/t2s` || "https://dev.dl2discovery.org/sts/api/v1/t2s",
    // Configuración para Speech-to-Text
    stt: {
      format: 'webm', // Formato de audio del navegador
      language: 'es'   // Idioma para transcripción
    },
    // Configuración para Text-to-Speech
    tts: {
      voice: 'es-ES-Neural2-A', // Voz a usar
      format: 'mp3'             // Formato de salida
    }
  }
};

class AIBackend {
  constructor() {
    this.sessions = new Map(); // Almacenar sesiones activas
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });

    this.setupExpress();
    this.setupWebSocket();
  }

  setupExpress() {
    // Middleware
    this.app.use(cors({
      origin: ['http://localhost:8080', 'http://127.0.0.1:8080', 'http://localhost:3000'],
      credentials: true
    }));
    this.app.use(express.json({ limit: '50mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }));

    // Servir archivos estáticos
    this.app.use(express.static('public'));
    this.app.use(express.static('.'));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        sessions: this.sessions.size,
        uptime: process.uptime(),
        config: {
          audioEnabled: !!(config.audio.s2tUrl && config.audio.t2sUrl),
          presetsAvailable: Object.keys(config.ia.presets),
          iaApiConnected: !!config.ia.apiUrl
        }
      });
    });

    // API info endpoint
    this.app.get('/api/info', (req, res) => {
      res.json({
        websocket: `ws://localhost:${config.port}`,
        presets: Object.keys(config.ia.presets),
        version: '1.0.0',
        supportedMessageTypes: [
          'test',
          'audio_chunk',
          'audio_end',
          'text_message',
          'set_preset',
          'reset_session'
        ]
      });
    });
  }

  setupWebSocket() {
    this.wss.on('connection', (ws, request) => {
      const sessionId = uuidv4();
      ws.sessionId = sessionId;

      // Configurar propiedades del WebSocket
      ws.isAlive = true;

      this.sessions.set(sessionId, {
        id: sessionId,
        iaSessionId: null,
        createdAt: new Date(),
        lastActivity: new Date(),
        ws: ws,
        currentPreset: 'genCharBot' // Preset por defecto
      });

      console.log(`🔌 New WebSocket connection: ${sessionId}`);

      this.sendMessage(ws, {
        type: 'connection',
        status: 'connected',
        sessionId: sessionId,
        supportedTypes: ['test', 'audio_chunk', 'audio_end', 'text_message', 'set_preset', 'reset_session'],
        defaultPreset: 'genCharBot'
      });

      // Manejar mensajes
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message.toString());
          await this.handleMessage(ws, data);
        } catch (error) {
          console.error('❌ Error processing message:', error);
          this.sendError(ws, 'Invalid message format', error.message);
        }
      });

      // Manejar desconexión
      ws.on('close', (code, reason) => {
        if (ws.sessionId) {
          this.sessions.delete(ws.sessionId);
          console.log(`🔌 WebSocket connection closed: ${ws.sessionId} (Code: ${code})`);
        }
      });

      // Manejar errores
      ws.on('error', (error) => {
        console.error(`❌ WebSocket error for session ${ws.sessionId}:`, error);
      });

      // Heartbeat para detectar conexiones muertas
      ws.on('pong', () => {
        ws.isAlive = true;
      });
    });

    // Ping/Pong para mantener conexiones activas
    const interval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          console.log(`🔌 Terminating dead connection: ${ws.sessionId}`);
          if (ws.sessionId) {
            this.sessions.delete(ws.sessionId);
          }
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // 30 segundos

    this.wss.on('close', () => {
      clearInterval(interval);
    });
  }

  async handleMessage(ws, data) {
    const session = this.sessions.get(ws.sessionId);
    if (!session) {
      this.sendError(ws, 'Session not found');
      return;
    }

    session.lastActivity = new Date();

    try {
      switch (data.type) {
        case 'test':
          await this.handleTestMessage(ws, data, session);
          break;

        case 'audio_chunk':
          await this.handleAudioChunk(ws, data, session);
          break;

        case 'audio_end':
          await this.processCompleteAudio(ws, session);
          break;

        case 'text_message':
          await this.handleTextMessage(ws, data, session);
          break;

        case 'set_preset':
          await this.handleSetPreset(ws, data, session);
          break;

        case 'reset_session':
          await this.handleResetSession(ws, session);
          break;

        default:
          console.log(`⚠️ Unknown message type: ${data.type}`);
          this.sendError(ws, 'Unknown message type', `Supported types: test, audio_chunk, audio_end, text_message, set_preset, reset_session`);
      }
    } catch (error) {
      console.error(`❌ Error handling message ${data.type}:`, error);
      this.sendError(ws, `Error processing ${data.type}`, error.message);
    }
  }

  async handleTestMessage(ws, data, session) {
    try {
      console.log(`🧪 Test message received from ${session.id}: ${JSON.stringify(data)}`);

      this.sendMessage(ws, {
        type: 'test_response',
        status: 'success',
        message: 'Test message received successfully!',
        echo: data,
        sessionId: session.id,
        timestamp: new Date().toISOString(),
        serverInfo: {
          version: '1.0.0',
          uptime: process.uptime(),
          activeConnections: this.sessions.size
        }
      });

    } catch (error) {
      console.error('❌ Error handling test message:', error);
      this.sendError(ws, 'Test message processing error', error.message);
    }
  }

  async handleAudioChunk(ws, data, session) {
    try {
      // Inicializar buffer de audio si no existe
      if (!session.audioBuffer) {
        session.audioBuffer = [];
      }

      // Convertir base64 a buffer y agregar al buffer de audio
      const audioChunk = Buffer.from(data.audioData, 'base64');
      session.audioBuffer.push(audioChunk);

      this.sendMessage(ws, {
        type: 'audio_chunk_received',
        chunkSize: audioChunk.length,
        totalChunks: session.audioBuffer.length
      });

    } catch (error) {
      console.error('❌ Error handling audio chunk:', error);
      this.sendError(ws, 'Audio chunk processing error', error.message);
    }
  }

  async processCompleteAudio(ws, session) {
    try {
      if (!session.audioBuffer || session.audioBuffer.length === 0) {
        this.sendError(ws, 'No audio data to process');
        return;
      }

      console.log(`🎵 Processing ${session.audioBuffer.length} audio chunks`);

      // Combinar todos los chunks de audio
      const completeAudio = Buffer.concat(session.audioBuffer);
      session.audioBuffer = []; // Limpiar buffer

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'speech_to_text',
        audioSize: completeAudio.length
      });

      // Procesar Speech-to-Text (simulado por ahora)
      const transcription = await this.speechToText(completeAudio);

      if (!transcription || transcription.trim() === '') {
        this.sendError(ws, 'No speech detected in audio');
        return;
      }

      console.log(`🗣️ Transcription: "${transcription}"`);

      this.sendMessage(ws, {
        type: 'transcription',
        text: transcription
      });

      // Procesar con IA
      await this.processWithIA(ws, transcription, session);

    } catch (error) {
      console.error('❌ Error processing complete audio:', error);
      this.sendError(ws, 'Audio processing failed', error.message);
    }
  }

  async handleTextMessage(ws, data, session) {
    try {
      if (!data.text || data.text.trim() === '') {
        this.sendError(ws, 'Empty text message');
        return;
      }

      console.log(`💬 Text message from ${session.id}: "${data.text}"`);
      await this.processWithIA(ws, data.text, session);

    } catch (error) {
      console.error('❌ Error handling text message:', error);
      this.sendError(ws, 'Text processing failed', error.message);
    }
  }

  async handleSetPreset(ws, data, session) {
    try {
      const { preset } = data;

      if (!preset || !config.ia.presets[preset]) {
        this.sendError(ws, 'Invalid preset', `Available presets: ${Object.keys(config.ia.presets).join(', ')}`);
        return;
      }

      session.currentPreset = preset;
      session.iaSessionId = null; // Reset IA session when changing preset

      console.log(`🔧 Preset changed to: ${preset} (${config.ia.presets[preset]}) for session ${session.id}`);

      this.sendMessage(ws, {
        type: 'preset_set',
        preset: preset,
        presetId: config.ia.presets[preset],
        sessionReset: true
      });

    } catch (error) {
      console.error('❌ Error setting preset:', error);
      this.sendError(ws, 'Preset setting failed', error.message);
    }
  }

  async handleResetSession(ws, session) {
    try {
      if (session.iaSessionId) {
        // Reset IA session
        await this.resetIASession(session.iaSessionId);
      }

      session.iaSessionId = null;
      session.audioBuffer = [];
      // Mantener currentPreset

      console.log(`🔄 Session reset: ${session.id}`);

      this.sendMessage(ws, {
        type: 'session_reset',
        status: 'success',
        sessionId: session.id,
        currentPreset: session.currentPreset
      });

    } catch (error) {
      console.error('❌ Error resetting session:', error);
      this.sendError(ws, 'Session reset failed', error.message);
    }
  }

  async speechToText(audioBuffer) {
    try {
      console.log(`🗣️ Processing STT for ${audioBuffer.length} bytes`);

      // SIMULACIÓN: En un entorno real, aquí llamarías a Google Cloud Speech API
      // Por ahora devolvemos texto simulado para testing
      const simulatedTranscriptions = [
        "Hola, ¿cómo estás?",
        "¿Puedes ayudarme con algo?",
        "Esto es una prueba de audio",
        "¿Cuál es el clima hoy?",
        "Háblame sobre inteligencia artificial"
      ];

      const randomTranscription = simulatedTranscriptions[Math.floor(Math.random() * simulatedTranscriptions.length)];

      // Simular delay de procesamiento
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log(`✅ STT completed (simulated): "${randomTranscription}"`);
      return randomTranscription;

    } catch (error) {
      console.error('❌ Speech-to-Text error:', error);
      throw new Error(`STT processing failed: ${error.message}`);
    }
  }

  async processWithIA(ws, text, session) {
    try {
      console.log(`🤖 Processing IA for session ${session.id}: "${text}"`);

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'ai_processing'
      });

      // Usar preset actual de la sesión
      const preset = session.currentPreset || 'genCharBot';
      const presetId = config.ia.presets[preset];

      // Preparar request para Model Gateway API
      const iaRequest = {
        id: {
          clt: ws.sessionId,
          ses: session.iaSessionId || undefined
        },
        preset: presetId,
        query: text
      };

      console.log(`🚀 Sending IA request to: ${config.ia.apiUrl}generate`);
      console.log(`📋 Request:`, JSON.stringify(iaRequest, null, 2));

      const response = await axios.post(
        `${config.ia.apiUrl}generate`,
        iaRequest,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': config.ia.apiKey
          },
          timeout: 60000 // 60 segundos timeout
        }
      );

      console.log(`📨 IA Response status: ${response.status}`);
      console.log(`📨 IA Response:`, JSON.stringify(response.data, null, 2));

      if (!response.data.ok) {
        throw new Error(response.data.message || 'IA processing failed');
      }

      // Actualizar session ID de la IA
      session.iaSessionId = response.data.id.ses;

      const iaResponse = response.data.output;

      console.log(`✅ IA response for ${session.id}: "${iaResponse}"`);

      this.sendMessage(ws, {
        type: 'ai_response',
        text: iaResponse,
        sessionId: session.iaSessionId,
        preset: preset,
        tokens: response.data.sizes || {}
      });

      // Procesar Text-to-Speech
      await this.processTextToSpeech(ws, iaResponse);

    } catch (error) {
      console.error('❌ IA processing error:', error);
      if (error.response) {
        console.error('📨 IA API Error Response:', error.response.data);
      }
      this.sendError(ws, 'IA processing failed', error.message);
    }
  }

  async processTextToSpeech(ws, text) {
    try {
      console.log(`🔊 Processing TTS for: "${text.substring(0, 50)}..."`);

      this.sendMessage(ws, {
        type: 'processing',
        stage: 'text_to_speech'
      });

      // SIMULACIÓN: En un entorno real, aquí llamarías a Google Cloud TTS API
      // Por ahora generamos audio simulado
      const simulatedAudioBase64 = this.generateSimulatedAudio();

      // Simular delay de procesamiento TTS
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log(`✅ TTS completed (simulated): ${simulatedAudioBase64.length} base64 chars`);

      this.sendMessage(ws, {
        type: 'audio_response',
        audioData: simulatedAudioBase64,
        format: 'mp3',
        size: simulatedAudioBase64.length,
        simulated: true
      });

    } catch (error) {
      console.error('❌ Text-to-Speech error:', error);
      this.sendError(ws, 'TTS processing failed', error.message);
    }
  }

  generateSimulatedAudio() {
    // Genera un string base64 simulado para audio MP3
    // En producción esto sería reemplazado por la llamada real a TTS
    const simulatedData = Buffer.from('simulated-audio-data-' + Date.now()).toString('base64');
    return simulatedData;
  }

  async resetIASession(iaSessionId) {
    try {
      console.log(`🔄 Resetting IA session: ${iaSessionId}`);

      const response = await axios.get(
        `${config.ia.apiUrl}reset/${iaSessionId}`,
        {
          headers: {
            'X-Api-Key': config.ia.apiKey
          }
        }
      );

      console.log('✅ IA session reset:', response.data);
      return response.data;

    } catch (error) {
      console.error('❌ Error resetting IA session:', error);
      throw error;
    }
  }

  sendMessage(ws, data) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(data));
        console.log(`📤 Sent message to ${ws.sessionId}: ${data.type}`);
      }
    } catch (error) {
      console.error('❌ Error sending message:', error);
    }
  }

  sendError(ws, error, details = null) {
    console.error(`❌ Sending error to ${ws.sessionId}: ${error}`, details ? `- ${details}` : '');
    this.sendMessage(ws, {
      type: 'error',
      error: error,
      details: details,
      timestamp: new Date().toISOString()
    });
  }

  // Cleanup de sesiones inactivas
  cleanupInactiveSessions() {
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutos
    let cleaned = 0;

    for (const [sessionId, session] of this.sessions) {
      if (now - session.lastActivity > maxInactiveTime) {
        console.log(`🧹 Cleaning up inactive session: ${sessionId}`);
        if (session.ws && session.ws.readyState === WebSocket.OPEN) {
          session.ws.close();
        }
        this.sessions.delete(sessionId);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} inactive sessions`);
    }
  }

  start() {
    // Iniciar cleanup periódico
    setInterval(() => {
      this.cleanupInactiveSessions();
    }, 5 * 60 * 1000); // Cada 5 minutos

    this.server.listen(config.port, () => {
      console.log(`\n🚀 AI WebSocket Backend started successfully!`);
      console.log(`📡 Server listening on port: ${config.port}`);
      console.log(`🌐 Health check: http://localhost:${config.port}/health`);
      console.log(`📊 API info: http://localhost:${config.port}/api/info`);
      console.log(`🔌 WebSocket endpoint: ws://localhost:${config.port}`);
      console.log(`\n🤖 AI Configuration:`);
      console.log(`   API URL: ${config.ia.apiUrl}`);
      console.log(`   API Key: ${config.ia.apiKey.substring(0, 8)}...`);
      console.log(`\n🎯 Available Presets:`);
      Object.entries(config.ia.presets).forEach(([key, value]) => {
        console.log(`   📋 ${key}: ${value}`);
      });
      console.log(`\n🎙️ Audio Backend Configuration:`);
      console.log(`   Speech-to-Text: ${config.audio.s2tUrl}`);
      console.log(`   Text-to-Speech: ${config.audio.t2sUrl}`);
      console.log(`   STT Language: ${config.audio.stt.language}`);
      console.log(`   TTS Voice: ${config.audio.tts.voice}`);
      console.log(`   TTS Format: ${config.audio.tts.format}`);
      console.log(`\n📋 Supported Message Types:`);
      console.log(`   🧪 test - Test connection`);
      console.log(`   🎤 audio_chunk - Audio data`);
      console.log(`   ⏹️ audio_end - End audio stream`);
      console.log(`   💬 text_message - Text input`);
      console.log(`   🔧 set_preset - Change AI preset`);
      console.log(`   🔄 reset_session - Reset conversation`);
      console.log(`\n✨ Ready to accept connections!\n`);
    });
  }
}

// Manejar cierre graceful del servidor
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM. Shutting down gracefully...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Inicializar y arrancar el backend
const backend = new AIBackend();
backend.start();
