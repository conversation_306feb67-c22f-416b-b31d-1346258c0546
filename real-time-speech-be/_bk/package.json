{"name": "websocket-ai-backend", "version": "1.0.0", "description": "Backend Node.js with WebSockets, Speech-to-Text, AI integration, and Text-to-Speech", "main": "serindexver.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["websocket", "ai", "speech-to-text", "text-to-speech", "google-cloud", "nodejs", "real-time"], "author": "Tu nombre", "license": "MIT", "dependencies": {"axios": "^1.6.0", "buffer": "^6.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.0", "socket.io": "^4.8.1", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}