// Enhanced server with LLM Gateway integration
import { WebSocketServer } from "ws";
import path from "node:path";
import fs from "node:fs";
import { fileURLToPath } from "node:url";
import { v4 as uuidv4 } from "uuid";
import * as fse from "fs-extra";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = Number(process.env.PORT || 3001);
const RECORDINGS_DIR = path.join(__dirname, "recordings");
await fse.ensureDir(RECORDINGS_DIR);

// === Configuración de servicios ===
const SPEECH_API_URL = process.env.VITE_SPEECH_API_URL || "https://dev.dl2discovery.org/sts/api/v1/";
const SPEECH_API_KEY = process.env.VITE_SPEECH_API_KEY || "b075e3a3-3cd8-4b65-ba5e-735e32ec3251";

// Nueva configuración para agentes
const MODEL_GATEWAY_URL = process.env.MODEL_GATEWAY_URL || "https://dev.dl2discovery.org/llm-api/v1";
const MODEL_GATEWAY_API_KEY = process.env.MODEL_GATEWAY_API_KEY || "your-api-key-here";
const DEFAULT_PRESET = process.env.PRESET_ENYGMA || "mapp-Claude_enygma_V2";

const SAMPLE_RATE = 16000;
const SILENCE_THRESHOLD = 45;
const SILENCE_MS = 1200;
const MIN_SEGMENT_MS = 1200;
const MAX_SEGMENT_MS = 10000;

function rmsInt16(buf) {
  if (!buf || buf.length === 0) return 0;
  let sum = 0;
  for (let i = 0; i < buf.length; i++) {
    const s = buf[i];
    sum += s * s;
  }
  return Math.sqrt(sum / buf.length);
}

function pcm16ToWavBuffer(int16arr, sampleRate = SAMPLE_RATE) {
  const numChannels = 1;
  const bytesPerSample = 2;
  const blockAlign = numChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  const dataSize = int16arr.length * bytesPerSample;
  const buffer = Buffer.alloc(44 + dataSize);

  buffer.write("RIFF", 0);
  buffer.writeUInt32LE(36 + dataSize, 4);
  buffer.write("WAVE", 8);
  buffer.write("fmt ", 12);
  buffer.writeUInt32LE(16, 16);
  buffer.writeUInt16LE(1, 20);
  buffer.writeUInt16LE(numChannels, 22);
  buffer.writeUInt32LE(sampleRate, 24);
  buffer.writeUInt32LE(byteRate, 28);
  buffer.writeUInt16LE(blockAlign, 32);
  buffer.writeUInt16LE(16, 34);
  buffer.write("data", 36);
  buffer.writeUInt32LE(dataSize, 40);

  const pcmBuf = Buffer.from(int16arr.buffer, int16arr.byteOffset, int16arr.byteLength);
  pcmBuf.copy(buffer, 44);
  return buffer;
}

// Transcripción de audio a texto
async function transcribeSegment(int16arr) {
  if (!SPEECH_API_URL || !SPEECH_API_KEY) return null;
  try {
    const wav = pcm16ToWavBuffer(int16arr);
    const form = new FormData();
    form.append("file", new Blob([wav], { type: "audio/wav" }), "segment.wav");
    form.append("sample_rate", String(SAMPLE_RATE));
    form.append("language", "es");

    const res = await fetch(`${SPEECH_API_URL.replace(/\/+$/, "")}/s2t`, {
      method: "POST",
      headers: { Authorization: `Bearer ${SPEECH_API_KEY}` },
      body: form,
    });

    const raw = await res.text();
    if (!res.ok) {
      console.warn("[s2t] HTTP", res.status, raw);
      return null;
    }

    let json = {};
    try {
      json = JSON.parse(raw);
    } catch {}

    const text = json?.text ?? json?.result?.text ?? json?.output ?? json?.transcript ?? json?.data?.text ?? null;
    return text?.trim() || null;
  } catch (e) {
    console.warn("[s2t] error:", e.message);
    return null;
  }
}

// NUEVA: Generar respuesta del agente
async function generateAgentResponse(userText, sessionId = null, preset = DEFAULT_PRESET) {
  if (!MODEL_GATEWAY_URL || !MODEL_GATEWAY_API_KEY) return null;

  try {
    const requestBody = {
      id: {
        clt: "voice-chat-client",
        ses: sessionId || uuidv4()
      },
      preset: preset,
      query: userText,
      model_params: {
        max_tokens: 200,
        temperature: 0.7
      }
    };

    const response = await fetch(`${MODEL_GATEWAY_URL}/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": MODEL_GATEWAY_API_KEY
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      console.warn("[agent] HTTP", response.status, await response.text());
      return null;
    }

    const result = await response.json();

    if (result.ok && result.output) {
      return {
        text: result.output,
        sessionId: result.id?.ses || sessionId
      };
    }

    return null;
  } catch (e) {
    console.warn("[agent] error:", e.message);
    return null;
  }
}

const wss = new WebSocketServer({ port: PORT });
console.log(`[ws-server] Listening on ws://localhost:${PORT}`);

wss.on("connection", (ws, req) => {
  const okPaths = new Set(["/stream", "/audio", "/", undefined]);
  if (!okPaths.has(req.url)) {
    ws.close(1008, "Invalid path");
    return;
  }

  console.log(`[ws] client connected (${req.url || "/"})`);

  // Estado de la sesión
  let sessionId = uuidv4();
  let currentPreset = DEFAULT_PRESET;

  // Escritura en fichero
  let writeStream = null;
  let filePath = null;
  let bytes = 0;

  // Segmentación para STT
  let segmentBuffers = [];
  let lastAudioAt = Date.now();
  let lastLogAt = 0;

  const startNewFile = (ext = "pcm") => {
    const id = uuidv4();
    const filename = `${Date.now()}-${id}.${ext}`;
    filePath = path.join(RECORDINGS_DIR, filename);
    writeStream = fs.createWriteStream(filePath);
    bytes = 0;

    ws.send(JSON.stringify({
      type: "ready",
      file: filename,
      sessionId: sessionId,
      preset: currentPreset
    }));

    console.log(`[ws] recording started → ${filename}`);
  };

  async function maybeCloseSegment(force = false) {
    const msSinceAudio = Date.now() - lastAudioAt;
    if (!force && msSinceAudio < SILENCE_MS) return;
    if (segmentBuffers.length === 0) return;

    const totalLen = segmentBuffers.reduce((acc, b) => acc + b.length, 0);
    const segMs = (totalLen / SAMPLE_RATE) * 1000;

    if (!force && segMs < MIN_SEGMENT_MS) return;

    // Concatenar segmento
    const merged = new Int16Array(totalLen);
    let off = 0;
    for (const b of segmentBuffers) {
      merged.set(b, off);
      off += b.length;
    }
    segmentBuffers = [];

    console.log(`[stt] segmento listo ${(segMs / 1000).toFixed(2)}s`);

    // 1. Transcribir audio
    const userText = await transcribeSegment(merged);
    if (!userText || !userText.trim()) {
      console.log("[s2t] (sin texto)");
      return;
    }

    console.log(`[user] ${userText}`);

    // Enviar transcripción al cliente
    try {
      ws.send(JSON.stringify({
        type: "transcript",
        text: userText.trim(),
        speaker: "user"
      }));
    } catch (e) {
      console.warn("Error enviando transcript:", e.message);
    }

    // 2. Generar respuesta del agente
    const agentResponse = await generateAgentResponse(userText, sessionId, currentPreset);
    if (agentResponse) {
      sessionId = agentResponse.sessionId; // Actualizar session ID
      console.log(`[agent] ${agentResponse.text}`);

      // Enviar respuesta del agente al cliente
      try {
        ws.send(JSON.stringify({
          type: "agent_response",
          text: agentResponse.text,
          speaker: "agent",
          sessionId: sessionId
        }));
      } catch (e) {
        console.warn("Error enviando agent response:", e.message);
      }
    } else {
      console.log("[agent] (sin respuesta)");
    }
  }

  const silenceTimer = setInterval(() => maybeCloseSegment(false), 200);

  ws.on("message", (data, isBinary) => {
    if (!isBinary) {
      try {
        const msg = JSON.parse(data.toString());

        if (msg.type === "start") {
          const ext = msg.mimeType?.includes("webm") ? "webm" : "pcm";
          if (!writeStream) startNewFile(ext);
        }
        else if (msg.type === "stop") {
          if (writeStream) {
            writeStream.end(() => {
              ws.send(JSON.stringify({
                type: "saved",
                file: path.basename(filePath),
                bytes,
              }));
              console.log(`[ws] recording saved → ${path.basename(filePath)} (${bytes} bytes)`);
            });
            writeStream = null;
          }
          void maybeCloseSegment(true);
        }
        else if (msg.type === "change_preset" && msg.preset) {
          currentPreset = msg.preset;
          console.log(`[ws] preset changed to: ${currentPreset}`);
          ws.send(JSON.stringify({
            type: "preset_changed",
            preset: currentPreset
          }));
        }
        else if (msg.type === "reset_session") {
          sessionId = uuidv4();
          console.log(`[ws] session reset: ${sessionId}`);
          ws.send(JSON.stringify({
            type: "session_reset",
            sessionId: sessionId
          }));
        }
      } catch (e) {
        console.warn("Error parsing message:", e.message);
      }
      return;
    }

    // Procesar datos de audio binarios
    if (!writeStream) startNewFile("pcm");
    writeStream.write(data);
    bytes += data.length;

    const int16 = new Int16Array(data.buffer, data.byteOffset, data.byteLength / 2);
    const level = rmsInt16(int16);

    const now = Date.now();
    if (now - lastLogAt > 500) {
      lastLogAt = now;
      const bars = Math.min(30, Math.floor((level / 32768) * 30));
      const vu = "▮".repeat(bars).padEnd(30, " ");
      process.stdout.write(`\r[vu] ${vu}  rms=${level.toFixed(0)}`);
    }

    if (level > SILENCE_THRESHOLD) {
      lastAudioAt = now;
    }

    const startedAgo = segmentBuffers.length
      ? (segmentBuffers.reduce((a, b) => a + b.length, 0) / SAMPLE_RATE) * 1000
      : 0;
    if (startedAgo > MAX_SEGMENT_MS) {
      void maybeCloseSegment(true);
    }

    segmentBuffers.push(int16);
  });

  ws.on("close", () => {
    clearInterval(silenceTimer);
    if (writeStream) {
      writeStream.end();
      console.log("\n[ws] client disconnected – stream closed");
    }
    void maybeCloseSegment(true);
  });

  ws.on("error", (err) => {
    console.error("[ws] error:", err.message);
  });
});
