// /src/hooks/useMicWebSocket.ts
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

type Status =
  | "idle"
  | "connecting"
  | "ready"
  | "recording"
  | "closed"
  | "error";

type HookReturn = {
  start: () => Promise<void>;
  stop: () => void;
  reset: () => void;
  status: Status;
  serverFile: string | null;
  bytesSent: number;
  log: string[];
};

// Helpers --------------------------------------------------------------
function floatToInt16(f32: Float32Array): Int16Array {
  const out = new Int16Array(f32.length);
  for (let i = 0; i < f32.length; i++) {
    let s = Math.max(-1, Math.min(1, f32[i]));
    out[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
  }
  return out;
}

function linearResampleInt16(
  input: Int16Array,
  inRate: number,
  outRate: number
): Int16Array {
  if (inRate === outRate) return input;
  const ratio = inRate / outRate;
  const outLen = Math.floor(input.length / ratio);
  const out = new Int16Array(outLen);
  let pos = 0;
  for (let i = 0; i < outLen; i++) {
    const idx = i * ratio;
    const i0 = Math.floor(idx);
    const i1 = Math.min(i0 + 1, input.length - 1);
    const frac = idx - i0;
    // linear interpolation in Int16 space
    out[i] = (input[i0] * (1 - frac) + input[i1] * frac) | 0;
  }
  return out;
}

function useStableCallback<T extends (...args: any[]) => any>(fn: T) {
  const ref = useRef(fn);
  ref.current = fn;
  return useCallback((...args: Parameters<T>) => ref.current(...args), []);
}

// Hook -----------------------------------------------------------------
export function useMicWebSocket(wsUrl?: string): HookReturn {
  const url = useMemo(
    () =>
      wsUrl ??
      (import.meta as any).env?.VITE_WS_URL ??
      "ws://localhost:8787/stream",
    [wsUrl]
  );

  const [status, setStatus] = useState<Status>("idle");
  const [serverFile, setServerFile] = useState<string | null>(null);
  const [bytesSent, setBytesSent] = useState<number>(0);
  const [log, setLog] = useState<string[]>([]);

  const wsRef = useRef<WebSocket | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const ctxRef = useRef<AudioContext | null>(null);
  const nodeRef = useRef<AudioWorkletNode | null>(null);
  const recordingRef = useRef<boolean>(false);

  const pushLog = useStableCallback((m: string) =>
    setLog((prev) => [m, ...prev].slice(0, 500))
  );

  const cleanup = useStableCallback(() => {
    try {
      nodeRef.current?.disconnect();
    } catch {}
    try {
      ctxRef.current?.close();
    } catch {}
    try {
      streamRef.current?.getTracks().forEach((t) => t.stop());
    } catch {}
    try {
      wsRef.current?.close();
    } catch {}
    nodeRef.current = null;
    ctxRef.current = null;
    streamRef.current = null;
    wsRef.current = null;
    recordingRef.current = false;
  });

  useEffect(() => () => cleanup(), [cleanup]);

  const prepare = useStableCallback(async () => {
    if (status !== "idle" && status !== "closed") return;
    setStatus("connecting");

    // 1) WS
    const ws = new WebSocket(url);
    ws.binaryType = "arraybuffer";
    wsRef.current = ws;

    await new Promise<void>((resolve, reject) => {
      ws.onopen = () => resolve();
      ws.onerror = (e) => reject(new Error("WebSocket error"));
    });

    ws.onmessage = (ev) => {
      try {
        const txt =
          typeof ev.data === "string"
            ? ev.data
            : new TextDecoder().decode(ev.data);
        const msg = JSON.parse(txt);
        if (msg?.file) setServerFile(String(msg.file));
        if (msg?.ready) setStatus("ready");
        if (msg?.log) pushLog(String(msg.log));
      } catch {
        pushLog("<<server>> " + String(ev.data));
      }
    };

    ws.addEventListener("message", (ev) => {
      try {
        const msg = JSON.parse(ev.data);
        if (msg.type === "ready") {
          // archivo creado en server
          console.log("ready:", msg.file);
        } else if (msg.type === "transcript") {
          // pinta el texto en tu UI
          const el = document.getElementById("transcripts");
          if (el) {
            const li = document.createElement("li");
            li.textContent = msg.text;
            el.appendChild(li);
          }
          console.log("🗣️", msg.text);
        } else if (msg.type === "saved") {
          console.log("saved:", msg.file, msg.bytes, "bytes");
        }
      } catch {}
    });

    ws.onclose = () => {
      pushLog("WebSocket closed");
      setStatus((s) => (s === "recording" ? "error" : "closed"));
    };

    // 2) MIC + CTX
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        channelCount: 1,
        noiseSuppression: true,
        echoCancellation: true,
      },
      video: false,
    });
    streamRef.current = stream;

    const ctx = new (window.AudioContext || (window as any).webkitAudioContext)(
      { sampleRate: 48000 }
    );
    ctxRef.current = ctx;

    try {
      await ctx.audioWorklet.addModule("/mic-processor.js");
    } catch (e) {
      setStatus("error");
      pushLog("No se pudo cargar el worklet /mic-processor.js");
      throw e;
    }

    const source = ctx.createMediaStreamSource(stream);
    const node = new AudioWorkletNode(ctx, "mic-processor");
    nodeRef.current = node;

    node.port.onmessage = (event: MessageEvent<Float32Array>) => {
      if (!recordingRef.current) return;
      const f32 = event.data;
      const i16 = floatToInt16(f32);
      const down = linearResampleInt16(i16, 48000, 16000);
      try {
        ws.send(down.buffer);
        setBytesSent((b) => b + down.byteLength);
      } catch (e) {
        pushLog("Error enviando por WS: " + (e as Error).message);
      }
    };

    // Mantener el grafo vivo pero sin grabar aún
    const gain = ctx.createGain();
    gain.gain.value = 0; // silencio
    source.connect(node).connect(gain).connect(ctx.destination);

    setStatus("ready");
    pushLog("Mic + WS listos");
  });

  const start = useStableCallback(async () => {
    if (status === "idle" || status === "closed") await prepare();
    if (ctxRef.current?.state === "suspended") await ctxRef.current.resume();
    recordingRef.current = true;
    setStatus("recording");
    pushLog("▶️ Grabando");
  });

  const stop = useStableCallback(() => {
    recordingRef.current = false;
    setStatus("ready");
    pushLog("⏹️ Parado (listo)");
  });

  const reset = useStableCallback(() => {
    cleanup();
    setServerFile(null);
    setBytesSent(0);
    setLog([]);
    setStatus("idle");
    pushLog("Reiniciado");
  });

  return { start, stop, reset, status, serverFile, bytesSent, log };
}

export default useMicWebSocket;
