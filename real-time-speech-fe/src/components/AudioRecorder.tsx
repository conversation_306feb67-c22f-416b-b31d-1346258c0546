import React, { useState, useEffect, useRef } from 'react';
import { useMicWebSocket } from "../hooks/useMicWebSocket";

interface Message {
  id: string;
  text: string;
  speaker: 'user' | 'agent';
  timestamp: Date;
}

interface PresetOption {
  id: string;
  name: string;
}

const PRESET_OPTIONS: PresetOption[] = [
  { id: "mapp-gen-char-bot", name: "Character Bot" },
  { id: "mapp-Claude_enygma_V2", name: "Claude Enygma V2" }
];

export default function AudioRecorder() {
  const { start, stop, reset, status, serverFile, bytesSent, log } = useMicWebSocket();

  const [messages, setMessages] = useState<Message[]>([]);
  const [currentPreset, setCurrentPreset] = useState<string>(PRESET_OPTIONS[1].id);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isAgentSpeaking, setIsAgentSpeaking] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Auto-scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // WebSocket message handler
  useEffect(() => {
    const wsUrl = import.meta.env?.VITE_WS_URL || "ws://localhost:3001/audio";

    const connectWebSocket = () => {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'ready':
              if (data.sessionId) setSessionId(data.sessionId);
              break;

            case 'transcript':
              if (data.text && data.speaker === 'user') {
                setMessages(prev => [...prev, {
                  id: Date.now().toString(),
                  text: data.text,
                  speaker: 'user',
                  timestamp: new Date()
                }]);
              }
              break;

            case 'agent_response':
              setIsAgentSpeaking(false);
              if (data.text && data.speaker === 'agent') {
                setMessages(prev => [...prev, {
                  id: Date.now().toString(),
                  text: data.text,
                  speaker: 'agent',
                  timestamp: new Date()
                }]);
                setSessionId(data.sessionId);

                // TODO: Aquí puedes integrar text-to-speech
                // speakText(data.text);
              }
              break;

            case 'preset_changed':
              console.log('Preset changed to:', data.preset);
              break;

            case 'session_reset':
              setMessages([]);
              setSessionId(data.sessionId);
              break;
          }
        } catch (e) {
          console.warn('Error parsing WebSocket message:', e);
        }
      };

      ws.onclose = () => {
        console.log('WebSocket closed, attempting to reconnect...');
        setTimeout(connectWebSocket, 2000);
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    };

    connectWebSocket();

    return () => {
      wsRef.current?.close();
    };
  }, []);

  const handlePresetChange = (newPreset: string) => {
    setCurrentPreset(newPreset);

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'change_preset',
        preset: newPreset
      }));
    }
  };

  const handleResetSession = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'reset_session'
      }));
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div style={{
      maxWidth: 800,
      margin: "2rem auto",
      fontFamily: "system-ui",
      padding: "0 1rem"
    }}>
      <h2>🎙️ Conversación por Voz en Tiempo Real</h2>

      {/* Status Panel */}
      <div style={{
        background: "#f5f5f5",
        padding: "1rem",
        borderRadius: 8,
        marginBottom: "1rem"
      }}>
        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <div>
            <strong>Estado: </strong>
            <span style={{
              color: status === 'recording' ? '#22c55e' : status === 'ready' ? '#3b82f6' : '#6b7280'
            }}>
              {status === 'recording' ? '🔴 Grabando' :
               status === 'ready' ? '🟢 Listo' :
               status === 'connecting' ? '🟡 Conectando' :
               '⚫ Desconectado'}
            </span>
          </div>

          <div>
            <label style={{ marginRight: "0.5rem" }}>Agente:</label>
            <select
              value={currentPreset}
              onChange={(e) => handlePresetChange(e.target.value)}
              style={{ padding: "0.25rem 0.5rem", borderRadius: 4 }}
            >
              {PRESET_OPTIONS.map(preset => (
                <option key={preset.id} value={preset.id}>
                  {preset.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {sessionId && (
          <div style={{ fontSize: "0.8rem", color: "#666", marginTop: "0.5rem" }}>
            Sesión: {sessionId.slice(0, 8)}...
          </div>
        )}
      </div>

      {/* Controls */}
      <div style={{ display: "flex", gap: 8, marginBottom: "1rem" }}>
        <button
          onClick={start}
          disabled={status === "connecting" || status === "recording"}
          style={{
            background: status === "recording" ? "#dc2626" : "#22c55e",
            color: "white",
            border: "none",
            padding: "0.75rem 1.5rem",
            borderRadius: 6,
            cursor: status === "connecting" || status === "recording" ? "not-allowed" : "pointer",
            fontSize: "1rem"
          }}
        >
          {status === "recording" ? "🔴 Grabando..." : "▶️ Iniciar"}
        </button>

        <button
          onClick={stop}
          disabled={status !== "recording" && status !== "ready"}
          style={{
            background: "#f59e0b",
            color: "white",
            border: "none",
            padding: "0.75rem 1.5rem",
            borderRadius: 6,
            cursor: status !== "recording" && status !== "ready" ? "not-allowed" : "pointer"
          }}
        >
          ⏸️ Pausar
        </button>

        <button
          onClick={reset}
          style={{
            background: "#6b7280",
            color: "white",
            border: "none",
            padding: "0.75rem 1.5rem",
            borderRadius: 6,
            cursor: "pointer"
          }}
        >
          🔄 Reset Audio
        </button>

        <button
          onClick={handleResetSession}
          style={{
            background: "#dc2626",
            color: "white",
            border: "none",
            padding: "0.75rem 1.5rem",
            borderRadius: 6,
            cursor: "pointer"
          }}
        >
          🗑️ Nueva Conversación
        </button>
      </div>

      {/* Conversation Messages */}
      <div style={{
        background: "#fff",
        border: "1px solid #e5e7eb",
        borderRadius: 8,
        height: "400px",
        overflowY: "auto",
        padding: "1rem",
        marginBottom: "1rem"
      }}>
        {messages.length === 0 ? (
          <div style={{
            textAlign: "center",
            color: "#6b7280",
            padding: "2rem",
            fontStyle: "italic"
          }}>
            Inicia una conversación presionando "Iniciar" y comenzando a hablar...
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              style={{
                display: "flex",
                justifyContent: message.speaker === 'user' ? "flex-end" : "flex-start",
                marginBottom: "1rem"
              }}
            >
              <div
                style={{
                  maxWidth: "70%",
                  padding: "0.75rem 1rem",
                  borderRadius: 12,
                  background: message.speaker === 'user' ? "#3b82f6" : "#f3f4f6",
                  color: message.speaker === 'user' ? "white" : "#1f2937",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
                }}
              >
                <div style={{ marginBottom: "0.25rem" }}>
                  {message.text}
                </div>
                <div style={{
                  fontSize: "0.7rem",
                  opacity: 0.7,
                  textAlign: "right"
                }}>
                  {message.speaker === 'user' ? '👤' : '🤖'} {formatTime(message.timestamp)}
                </div>
              </div>
            </div>
          ))
        )}

        {isAgentSpeaking && (
          <div style={{
            display: "flex",
            justifyContent: "flex-start",
            marginBottom: "1rem"
          }}>
            <div style={{
              padding: "0.75rem 1rem",
              borderRadius: 12,
              background: "#f3f4f6",
              color: "#1f2937",
              fontStyle: "italic"
            }}>
              🤖 Generando respuesta...
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Stats */}
      <div style={{ display: "flex", gap: "1rem", fontSize: "0.9rem", color: "#6b7280" }}>
        <span>Bytes enviados: {bytesSent.toLocaleString()}</span>
        {serverFile && <span>Archivo: {serverFile}</span>}
      </div>

      {/* Logs (Collapsible) */}
      <details style={{ marginTop: 16 }}>
        <summary style={{ cursor: "pointer", fontWeight: "bold" }}>
          Logs del Sistema ({log.length})
        </summary>
        <pre style={{
          background: "#111",
          color: "#0f0",
          padding: 12,
          borderRadius: 8,
          maxHeight: 200,
          overflow: "auto",
          fontSize: "0.8rem",
          marginTop: "0.5rem"
        }}>
          {log.map((l, i) =>
            `${String(log.length - i).padStart(3, "0")}: ${l}\n`
          )}
        </pre>
      </details>
    </div>
  );
}
